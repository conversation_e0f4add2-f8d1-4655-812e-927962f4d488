#!/usr/bin/env python3
"""
Simple test to verify parallel processing limits work.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.image_utils import MAX_PARALLEL_IMAGE_REQUESTS, get_image_processing_semaphore


async def test_semaphore_limit():
    """Test that the semaphore respects the configured limit."""
    print(f"Testing semaphore with limit: {MAX_PARALLEL_IMAGE_REQUESTS}")
    
    semaphore = get_image_processing_semaphore()
    print(f"Semaphore created with value: {semaphore._value}")
    
    # Test acquiring semaphore slots
    acquired_count = 0
    
    async def acquire_slot(slot_id):
        nonlocal acquired_count
        async with semaphore:
            acquired_count += 1
            print(f"  Slot {slot_id}: Acquired (total active: {acquired_count})")
            await asyncio.sleep(0.1)
            acquired_count -= 1
            print(f"  Slot {slot_id}: Released (total active: {acquired_count})")
    
    # Try to acquire more slots than the limit
    tasks = [acquire_slot(i) for i in range(MAX_PARALLEL_IMAGE_REQUESTS + 5)]
    
    start_time = asyncio.get_event_loop().time()
    await asyncio.gather(*tasks)
    end_time = asyncio.get_event_loop().time()
    
    print(f"All tasks completed in {end_time - start_time:.2f} seconds")
    print(f"✅ Semaphore limit test completed")


async def test_configuration():
    """Test configuration values."""
    print(f"\nConfiguration Test:")
    print(f"  MAX_PARALLEL_IMAGE_REQUESTS: {MAX_PARALLEL_IMAGE_REQUESTS}")
    print(f"  Type: {type(MAX_PARALLEL_IMAGE_REQUESTS)}")
    
    # Test environment variable
    original_value = os.environ.get("MAX_PARALLEL_IMAGE_REQUESTS")
    os.environ["MAX_PARALLEL_IMAGE_REQUESTS"] = "7"
    
    # Import again to test env var
    import importlib
    import utils.image_utils
    importlib.reload(utils.image_utils)
    
    new_value = utils.image_utils.MAX_PARALLEL_IMAGE_REQUESTS
    print(f"  After setting env var to 7: {new_value}")
    
    # Cleanup
    if original_value is not None:
        os.environ["MAX_PARALLEL_IMAGE_REQUESTS"] = original_value
    else:
        del os.environ["MAX_PARALLEL_IMAGE_REQUESTS"]
    
    print(f"✅ Configuration test completed")


async def main():
    """Run tests."""
    print("🚀 Simple Parallel Processing Test\n")
    
    try:
        await test_semaphore_limit()
        await test_configuration()
        
        print(f"\n✅ All tests completed successfully!")
        print(f"\n💡 Current limit: {MAX_PARALLEL_IMAGE_REQUESTS}")
        print(f"   To change: export MAX_PARALLEL_IMAGE_REQUESTS=15")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
