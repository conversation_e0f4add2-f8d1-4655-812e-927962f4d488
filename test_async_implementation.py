#!/usr/bin/env python3
"""
Test script to verify async implementation works correctly.
"""

import asyncio
import time
import os
import sys
from typing import List, Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_analyzer.llm_analyzer import LLMAnalyzer
from llm_analyzer.cache_manager import CacheManager
from utils.image_utils import encode_image_array_from_urls_to_base64, normalize_ebay_url


async def test_cache_manager():
    """Test async cache manager operations."""
    print("Testing async cache manager...")
    
    cache_manager = CacheManager()
    
    # Test set and get
    test_key = "test_key_async"
    test_value = {"test": "data", "timestamp": time.time()}
    
    await cache_manager.set(test_key, test_value)
    retrieved_value = await cache_manager.get(test_key)
    
    assert retrieved_value == test_value, f"Cache test failed: {retrieved_value} != {test_value}"
    print("✓ Cache set/get operations work correctly")
    
    # Test delete
    await cache_manager.delete(test_key)
    deleted_value = await cache_manager.get(test_key)
    
    assert deleted_value is None, f"Cache delete failed: {deleted_value} should be None"
    print("✓ Cache delete operation works correctly")
    
    cache_manager.close()


async def test_image_utils():
    """Test async image utilities."""
    print("Testing async image utilities...")
    
    # Test URL normalization
    test_url = "https://i.ebayimg.com/images/g/abc123/z/def456/"
    normalized = normalize_ebay_url(test_url)
    print(f"✓ URL normalization works: {test_url} -> {normalized}")
    
    # Test with empty list
    empty_result = await encode_image_array_from_urls_to_base64([])
    assert empty_result == [], f"Empty list test failed: {empty_result}"
    print("✓ Empty image list handling works correctly")
    
    # Test with invalid URLs (should handle gracefully)
    invalid_urls = ["http://invalid-url-that-does-not-exist.com/image.jpg"]
    start_time = time.time()
    result = await encode_image_array_from_urls_to_base64(invalid_urls)
    end_time = time.time()
    
    print(f"✓ Invalid URL handling completed in {end_time - start_time:.2f}s")
    print(f"  Result: {len(result)} items, first item: {type(result[0]) if result else 'None'}")


async def test_llm_analyzer():
    """Test async LLM analyzer."""
    print("Testing async LLM analyzer...")
    
    analyzer = LLMAnalyzer()
    
    # Test async initialization
    await analyzer._async_init()
    print("✓ Async initialization completed")
    
    # Test system message operations
    original_message = analyzer.get_system_message()
    print(f"✓ System message loaded: {len(original_message)} characters")
    
    # Test update system message
    test_message = "Test system message for async verification."
    await analyzer.update_system_message(test_message)
    updated_message = analyzer.get_system_message()
    
    assert updated_message == test_message, f"System message update failed"
    print("✓ System message update works correctly")
    
    # Restore original message
    await analyzer.update_system_message(original_message)
    
    # Test cache key generation
    test_params = {
        "test_param": "test_value",
        "images": ["http://example.com/image1.jpg", "http://example.com/image2.jpg"]
    }
    
    cache_key = analyzer.cache_manager.generate_key(
        model_string="test-model",
        system_message=original_message,
        request_params=test_params
    )
    
    assert cache_key.startswith("llm_request_hash::"), f"Cache key format incorrect: {cache_key}"
    print("✓ Cache key generation works correctly")


async def test_concurrent_operations():
    """Test concurrent async operations."""
    print("Testing concurrent async operations...")
    
    cache_manager = CacheManager()
    
    # Test concurrent cache operations
    async def cache_operation(i: int):
        key = f"concurrent_test_{i}"
        value = {"index": i, "timestamp": time.time()}
        await cache_manager.set(key, value)
        retrieved = await cache_manager.get(key)
        await cache_manager.delete(key)
        return retrieved == value
    
    start_time = time.time()
    results = await asyncio.gather(*[cache_operation(i) for i in range(10)])
    end_time = time.time()
    
    assert all(results), f"Concurrent cache operations failed: {results}"
    print(f"✓ 10 concurrent cache operations completed in {end_time - start_time:.2f}s")
    
    cache_manager.close()


async def test_performance_comparison():
    """Compare async vs sync performance (simulated)."""
    print("Testing performance characteristics...")
    
    # Test async image processing with multiple URLs
    test_urls = [
        "https://httpbin.org/delay/1",  # Simulated slow endpoint
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1"
    ]
    
    start_time = time.time()
    # This would normally process in parallel
    print(f"✓ Async operations would process {len(test_urls)} URLs concurrently")
    print(f"  Expected time: ~1 second (parallel) vs ~{len(test_urls)} seconds (sequential)")
    
    # Test cache operations
    cache_manager = CacheManager()
    
    start_time = time.time()
    for i in range(5):
        await cache_manager.set(f"perf_test_{i}", {"data": i})
        await cache_manager.get(f"perf_test_{i}")
    end_time = time.time()
    
    print(f"✓ 5 async cache operations completed in {end_time - start_time:.3f}s")
    
    cache_manager.close()


async def main():
    """Run all async tests."""
    print("🚀 Starting async implementation tests...\n")
    
    try:
        await test_cache_manager()
        print()
        
        await test_image_utils()
        print()
        
        await test_llm_analyzer()
        print()
        
        await test_concurrent_operations()
        print()
        
        await test_performance_comparison()
        print()
        
        print("✅ All async implementation tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
