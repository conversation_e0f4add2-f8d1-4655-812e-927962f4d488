import os
import aiofiles
from fastapi import FastAP<PERSON>, Form, Request
from fastapi.responses import HTMLResponse

from llm_analyzer.llm_analyzer import LLMAnalyzer


def register_edit_save_prompt_endpoints(app: FastAPI, llm_analyzer: LLMAnalyzer):
    @app.post("/prompt_save", response_class=HTMLResponse)
    async def prompt_save(
        request: Request,
        type: str = Form("strips"),
        saved: bool = Form(False)
    ):
        """Route handler for displaying the edit prompt form"""

        if llm_analyzer is None:
            return HTMLResponse(content="Invalid product type", status_code=400)

        description = f"Modify the system prompt used for {type} analysis. This prompt instructs the AI model on how to analyze {type} data."
        default_path_segment = "default_system_message.txt"

        current_prompt = llm_analyzer.get_system_message()

        # Path to the default prompt file, adjusted for utils/prompt_save.py
        default_system_message_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "_prompts", default_path_segment)

        try:
            async with aiofiles.open(default_system_message_path, "r") as f:
                default_prompt = await f.read()
        except FileNotFoundError:
            return HTMLResponse(content=f"Default prompt file not found for type '{type}' at {default_system_message_path}", status_code=500)

        # Escape any quotes in the default prompt to avoid breaking the JavaScript
        default_prompt_js = default_prompt.replace('\\', '\\\\').replace("'", "\\'").replace("\n", "\\n")

        success_message = """
        <div style="background-color: #dff0d8; color: #3c763d; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
            <strong>Success!</strong> Your changes have been saved.
        </div>
        """ if saved else ""

        error_message = "" # Placeholder for potential future error messages

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Edit System Prompt</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                }}
                h1 {{
                    color: #333;
                }}
                .form-container {{
                    max-width: 800px;
                    margin: 0 auto;
                }}
                textarea {{
                    width: 100%;
                    height: 400px;
                    padding: 10px;
                    margin-bottom: 20px;
                    font-family: monospace;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }}
                .button-container {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}
                button {{
                    padding: 10px 20px;
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                }}
                .cancel-button {{
                    background-color: #f44336;
                }}
                button:hover {{
                    opacity: 0.8;
                }}
                .reset-link {{
                    margin-top: 5px; /* Adjusted for alignment */
                    display: inline-block;
                    color: #2196F3;
                    text-decoration: none;
                    font-size: 14px;
                    cursor: pointer;
                }}
                .reset-link:hover {{
                    text-decoration: underline;
                }}
            </style>
            <script>
                function resetToDefault() {{
                    document.getElementById('promptTextarea').value = '{default_prompt_js}';
                    return false;
                }}
            </script>
        </head>
        <body>
            <div class="form-container">
                <h1>Edit System Prompt for {type.capitalize()}</h1>
                {success_message}
                {error_message}
                <p>{description}</p>

                <form action="/save_prompt" method="post">
                    <input type="hidden" name="type" value="{type}">
                    <textarea id="promptTextarea" name="prompt" required>{current_prompt}</textarea>
                    <div class="button-container">
                        <a href="about:blank" style="text-decoration: none;">
                            <button type="button" class="cancel-button">Cancel</button>
                        </a>
                        <a href="#" onclick="return resetToDefault();" class="reset-link">Reset to Default Prompt</a>
                        <button type="submit">Save Changes</button>
                    </div>
                </form>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)

    @app.post("/save_prompt", response_class=HTMLResponse)
    async def save_prompt(
        prompt: str = Form(...),
        type: str = Form("strips")
    ):
        """Route handler for saving the edited prompt"""
        if llm_analyzer is None:
            return HTMLResponse(content="Invalid product type", status_code=400)

        await llm_analyzer.update_system_message(prompt)

        # Redirect back to the edit page with a success indicator
        # Construct the redirect URL to include the 'saved=true' query parameter
        # This requires the 'request' object to build the URL, or a fixed path.
        # For simplicity, returning a success message page as in the original match.py
        # If redirection is preferred, the prompt_save route would need to handle 'saved' query param.
        # The original match.py's save_prompt_route returned a simple message, not a redirect.
        # The prompt_save_route in match.py was designed to show a success message if saved=true was passed in the form.
        # To make this work, save_prompt should redirect to prompt_save with saved=true.
        # However, the user's provided match.py save_prompt_route returns a separate success page.
        # I will follow the structure of the provided save_prompt_route from match.py.

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Prompt Updated</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    text-align: center;
                }}
                .message {{
                    margin: 50px auto;
                    padding: 20px;
                    max-width: 500px;
                    background-color: #f5f5f5;
                    border-radius: 5px;
                }}
                .close-link {{ /* Changed from home-link to close-link */
                    display: inline-block;
                    margin-top: 20px;
                    padding: 10px 20px;
                    background-color: #4CAF50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                }}
                .close-link:hover {{
                    opacity: 0.8;
                }}
            </style>
        </head>
        <body>
            <div class="message">
                <h2>System prompt for {type.capitalize()} has been changed.</h2>
                <!-- Provide a way to close the tab/window or go back -->
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)