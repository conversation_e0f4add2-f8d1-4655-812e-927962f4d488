# Parallel Processing Limits Configuration

## Overview
The image processing system now includes configurable limits for parallel requests to prevent resource exhaustion and ensure stable performance.

## Configuration

### Environment Variable
Set the maximum number of parallel image processing requests:

```bash
# In your environment
export MAX_PARALLEL_IMAGE_REQUESTS=15

# Or in your .env file
MAX_PARALLEL_IMAGE_REQUESTS=15
```

### Default Value
If not specified, the default limit is **15 parallel requests**.

## How It Works

### Semaphore Control
The system uses `asyncio.Semaphore` to control the number of concurrent image processing operations:

```python
# Global semaphore to limit parallel image processing
_IMAGE_PROCESSING_SEMAPHORE = None

def get_image_processing_semaphore():
    global _IMAGE_PROCESSING_SEMAPHORE
    if _IMAGE_PROCESSING_SEMAPHORE is None:
        _IMAGE_PROCESSING_SEMAPHORE = asyncio.Semaphore(MAX_PARALLEL_IMAGE_REQUESTS)
    return _IMAGE_PROCESSING_SEMAPHORE
```

### Processing Flow
1. **Request Queuing**: When processing multiple images, each request acquires a semaphore slot
2. **Parallel Execution**: Up to `MAX_PARALLEL_IMAGE_REQUESTS` images process simultaneously
3. **Automatic Queueing**: Additional requests wait until a slot becomes available
4. **Resource Protection**: Prevents overwhelming the system with too many concurrent requests

## Benefits

### Resource Management
- **Memory Control**: Limits memory usage from concurrent image processing
- **Network Efficiency**: Prevents overwhelming external image servers
- **CPU Usage**: Maintains reasonable CPU utilization
- **File Descriptors**: Reduces file descriptor usage

### Performance Optimization
- **Throughput**: Optimal balance between speed and resource usage
- **Stability**: Prevents system overload under high load
- **Scalability**: Configurable based on system capabilities

## Usage Examples

### Processing Large Batches
```python
# Process 100 images with max 15 parallel requests
image_urls = [f"http://example.com/image{i}.jpg" for i in range(100)]
results = await encode_image_array_from_urls_to_base64(image_urls)

# Output: "Processing 100 images with max 15 parallel requests"
```

### Different Configurations
```bash
# For high-performance servers
export MAX_PARALLEL_IMAGE_REQUESTS=25

# For resource-constrained environments
export MAX_PARALLEL_IMAGE_REQUESTS=5

# For development/testing
export MAX_PARALLEL_IMAGE_REQUESTS=3
```

## Testing

### Test Parallel Limits
Run the test script to verify the limits work correctly:

```bash
python test_parallel_limit.py
```

### Expected Output
```
🚀 Testing Parallel Image Processing Limits
Processing 25 images with max 15 parallel requests
Results:
  Total URLs processed: 25
  Max concurrent requests: 15
  Configured limit: 15
✅ PASS: Parallel limit respected (15 <= 15)
```

## Monitoring

### Log Output
The system logs the processing configuration:
```
Processing 50 images with max 15 parallel requests
Total time to encode 50 images: 12.34 seconds
```

### Performance Metrics
Monitor these metrics to optimize your configuration:
- **Processing time per batch**
- **Memory usage during processing**
- **Network connection count**
- **CPU utilization**

## Recommended Settings

### By System Type

#### Development Environment
```bash
MAX_PARALLEL_IMAGE_REQUESTS=5
```
- Lower resource usage
- Easier debugging
- Stable performance

#### Production Server (Standard)
```bash
MAX_PARALLEL_IMAGE_REQUESTS=15
```
- Balanced performance
- Good resource utilization
- Default recommended setting

#### High-Performance Server
```bash
MAX_PARALLEL_IMAGE_REQUESTS=25
```
- Maximum throughput
- Requires adequate resources
- Monitor system performance

#### Resource-Constrained Environment
```bash
MAX_PARALLEL_IMAGE_REQUESTS=3
```
- Minimal resource usage
- Slower processing
- Stable under constraints

### By Use Case

#### Batch Processing
- **Large batches**: 20-25 parallel requests
- **Regular batches**: 15 parallel requests
- **Small batches**: 10 parallel requests

#### Real-time Processing
- **Interactive use**: 10-15 parallel requests
- **API responses**: 15 parallel requests
- **Background tasks**: 20+ parallel requests

## Troubleshooting

### Common Issues

#### Too High Limit
**Symptoms**: Memory errors, connection timeouts, system slowdown
**Solution**: Reduce `MAX_PARALLEL_IMAGE_REQUESTS`

#### Too Low Limit
**Symptoms**: Slow processing, underutilized resources
**Solution**: Increase `MAX_PARALLEL_IMAGE_REQUESTS`

#### Environment Variable Not Working
**Symptoms**: Default value (15) always used
**Solution**: 
1. Check environment variable spelling
2. Restart the application
3. Verify .env file location

### Performance Tuning

#### Finding Optimal Value
1. Start with default (15)
2. Monitor system resources
3. Increase gradually while monitoring
4. Stop when performance degrades
5. Set to 80% of maximum stable value

#### System Resource Monitoring
```bash
# Monitor memory usage
top -p $(pgrep -f uvicorn)

# Monitor network connections
netstat -an | grep :80 | wc -l

# Monitor file descriptors
lsof -p $(pgrep -f uvicorn) | wc -l
```

## Integration with Other Limits

### HTTP Session Limits
- **Total connections**: 10 per session
- **Per-host connections**: 5 per host
- **Image processing**: Configurable (default 15)

### Relationship
The image processing limit works with HTTP connection limits:
- Image processing limit controls **how many images** process simultaneously
- HTTP connection limits control **how many network connections** are used
- Both work together to prevent resource exhaustion

## Future Enhancements

### Potential Improvements
1. **Dynamic Limits**: Adjust based on system load
2. **Per-Host Limits**: Different limits for different image sources
3. **Priority Queuing**: Process important images first
4. **Load Balancing**: Distribute across multiple workers

### Monitoring Integration
1. **Metrics Collection**: Track processing statistics
2. **Alerting**: Notify when limits are consistently hit
3. **Auto-scaling**: Adjust limits based on performance
4. **Dashboard**: Visual monitoring of processing queues
