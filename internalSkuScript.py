# Configure SSL and proxy before any imports
from utils.prompt_editor import register_edit_save_prompt_endpoints
from llm_analyzer.llm_analyzer import LLMAnalyzer  # Updated import
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import FastAPI, Request, Body
from llm_analyzer.analysis_renderer import render_llm_analysis_page
import os

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Now import the rest of the modules

app = FastAPI()
# Create a generic model analyzer
llm_analyzer_instance = LLMAnalyzer()

# Register the analyzer with the generic endpoint
# Using a generic key "product" for registration

register_edit_save_prompt_endpoints(app, llm_analyzer_instance)


@app.get("/", response_class=HTMLResponse)
async def root():
    """Root route that serves as a blank landing page"""
    return HTMLResponse(content="")


@app.post("/match_mydata", response_class=HTMLResponse)
async def product_analysis_route(  # Renamed function for clarity
    request: Request,
    data: dict = Body(...)
):
    """Route handler that delegates to the render_llm_analysis_page module for product analysis or returns JSON."""
    response_type = data.get("response_type")
    if response_type == "json":
        try:
            analysis_result = await llm_analyzer_instance(params=data)
            return JSONResponse(content=analysis_result)
        except Exception as e:
            # Consider more specific error handling or logging
            return JSONResponse(content={"error": str(e)}, status_code=500)
    else:
        return await render_llm_analysis_page(request, data, llm_analyzer_instance)
