import os
from fastapi import Request
from fastapi.responses import HTMLResponse
from typing import Dict, Any
from jinja2 import Environment, FileSystemLoader

from llm_analyzer.llm_analyzer import LLMAnalyzer

async def render_llm_analysis_page(
    request: Request,
    data: Dict[str, Any],
    analyzer: LLMAnalyzer,  # Added analyzer parameter
) -> HTMLResponse:
    """
    Generic endpoint to process data using specified product type model.
    Returns HTML response with analysis results.

    Args:
        request: FastAPI request object
        data: Request data dictionary
    """


    result: Dict[str, Any] = {}  # Initialize result
    try:
        # 'data' is named 'params' in the original context of render_llm_analysis_page
        result = await analyzer(params=data)
    except Exception as e:
        # Handle exceptions from LLMAnalyzer, e.g., return an error HTMLResponse
        error_html = f"<h1>Error during analysis: {str(e)}</h1>"
        return HTMLResponse(content=error_html, status_code=500)

    # Setup Jinja2 environment
    # The template loader should point to the '_templates' directory (relative to the project root)
    template_env = Environment(loader=FileSystemLoader("_templates"))

    # Keep the "Edit Prompt" link HTML generation
    prompt_save_form_html = f"""
    <div style="margin-top: 5px; margin-bottom: 5px; text-align: right; font-size: 12px;">
        <form action="/prompt_save" method="post" style="display: inline;">
            <input type="hidden" name="type" value="">
            <button type="submit" style="background: none; border: none; color: #666; text-decoration: underline; cursor: pointer; padding: 0; font: inherit;">
                Edit System Prompt
            </button>
        </form>
    </div>
    """

    # Load the template
    template = template_env.get_template("analysis_result.html")

    # Render the template
    html_content = template.render(analysis_data=result, prompt_save_form_html=prompt_save_form_html)

    return HTMLResponse(content=html_content)
