<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Results Dashboard</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            box-shadow: var(--box-shadow);
        }
        
        .dashboard-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }
        
        .dashboard-header p {
            margin: 5px 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .dashboard-content {
            background-color: white;
            padding: 30px;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .data-table thead th {
            background-color: var(--secondary-color);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
            font-size: 16px;
        }
        
        .data-table tbody tr:nth-child(even) {
            background-color: rgba(236, 240, 241, 0.5);
        }
        
        .data-table tbody td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .status-cell {
            font-weight: 500;
        }
        
        .status-error {
            color: var(--danger-color);
            background-color: rgba(231, 76, 60, 0.1);
        }
        
        .status-warning {
            color: var(--warning-color);
            background-color: rgba(243, 156, 18, 0.1);
        }
        
        .status-success {
            color: var(--success-color);
            background-color: rgba(46, 204, 113, 0.1);
        }
        
        .section-divider {
            border-top: 1px solid #eee;
            margin: 25px 0;
            padding-top: 20px;
        }
        
        .section-title {
            color: var(--secondary-color);
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 500;
        }
        
        .form-container {
            background-color: var(--light-color);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-top: 20px;
        }
        
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .badge-error {
            background-color: var(--danger-color);
            color: white;
        }
        
        .badge-warning {
            background-color: var(--warning-color);
            color: white;
        }
        
        .badge-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .badge-info {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .dashboard-content {
                padding: 15px;
            }
            
            .data-table thead th,
            .data-table tbody td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <h1>Analysis Results Dashboard</h1>
            <p>Comprehensive overview of system diagnostics and evaluations</p>
        </div>
        
        <div class="dashboard-content">
            <div class="section-title">Attribute Status Overview</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Attribute</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                {% for attr, value in analysis_data.items() %}
                    {% if attr.lower() == "visual evaluation" %}
                    <tr>
                        <td colspan="2" class="section-divider">
                            <div class="section-title">Visual Evaluation Results</div>
                        </td>
                    </tr>
                    {% endif %}

                    {% if (value is true or (value is string and value.lower() == "yes")) and attr.lower() != "visual evaluation" %}
                    <tr>
                        <td>{{ attr }}</td>
                        <td class="status-cell status-error">
                            <span class="badge badge-error">Failed</span> {{ value }}
                        </td>
                    </tr>
                    {% elif value is string and ("warning" in value.lower() or "caution" in value.lower()) %}
                    <tr>
                        <td>{{ attr }}</td>
                        <td class="status-cell status-warning">
                            <span class="badge badge-warning">Warning</span> {{ value }}
                        </td>
                    </tr>
                    {% elif value is string and ("pass" in value.lower() or "success" in value.lower() or "good" in value.lower()) %}
                    <tr>
                        <td>{{ attr }}</td>
                        <td class="status-cell status-success">
                            <span class="badge badge-success">Passed</span> {{ value }}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td>{{ attr }}</td>
                        <td class="status-cell">
                            {{ value }}
                        </td>
                    </tr>
                    {% endif %}
                {% endfor %}
                </tbody>
            </table>
            
            <div class="form-container">
                {{ prompt_save_form_html | safe }}
            </div>
        </div>
    </div>
</body>
</html>