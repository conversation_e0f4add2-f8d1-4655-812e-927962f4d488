# LiteLLM Configuration
LITELLM_MODEL_STRING="gpt-3.5-turbo" # Example: Can be any model LiteLLM supports, e.g., "gemini/gemini-1.5-flash", "claude-3-opus-20240229"

# Provider API Keys (set only the ones you use)
# OPENAI_API_KEY="sk-..."
# ANTHROPIC_API_KEY="sk-ant-..."
# GOOGLE_API_KEY="..."
# AZURE_API_KEY="..."
# COHERE_API_KEY="..."
# Add other keys as needed based on the chosen model in LITELLM_MODEL_STRING