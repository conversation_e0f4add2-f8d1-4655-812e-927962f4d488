#!/usr/bin/env python3
"""
Test script to verify API endpoints work with async implementation.
"""

import asyncio
import aiohttp
import json
import time


async def test_root_endpoint():
    """Test the root endpoint."""
    async with aiohttp.ClientSession() as session:
        async with session.get('http://127.0.0.1:8000/') as response:
            assert response.status == 200
            content = await response.text()
            print("✓ Root endpoint works correctly")
            return True


async def test_prompt_save_endpoint():
    """Test the prompt save endpoint."""
    async with aiohttp.ClientSession() as session:
        data = {
            'type': 'test',
            'saved': False
        }
        async with session.post('http://127.0.0.1:8000/prompt_save', data=data) as response:
            assert response.status == 200
            content = await response.text()
            assert 'Edit System Prompt' in content
            print("✓ Prompt save endpoint works correctly")
            return True


async def test_match_mydata_json():
    """Test the match_mydata endpoint with JSON response."""
    async with aiohttp.ClientSession() as session:
        test_data = {
            'response_type': 'json',
            'test_param': 'test_value',
            'description': 'Test product description'
        }
        
        start_time = time.time()
        async with session.post(
            'http://127.0.0.1:8000/match_mydata', 
            json=test_data,
            headers={'Content-Type': 'application/json'}
        ) as response:
            end_time = time.time()
            
            print(f"API call completed in {end_time - start_time:.2f} seconds")
            
            if response.status == 200:
                content = await response.json()
                print("✓ JSON endpoint works correctly")
                print(f"  Response keys: {list(content.keys())}")
                return True
            else:
                content = await response.text()
                print(f"⚠ JSON endpoint returned status {response.status}")
                print(f"  Response: {content[:200]}...")
                return False


async def test_match_mydata_html():
    """Test the match_mydata endpoint with HTML response."""
    async with aiohttp.ClientSession() as session:
        test_data = {
            'response_type': 'html',
            'test_param': 'test_value',
            'description': 'Test product description'
        }
        
        start_time = time.time()
        async with session.post(
            'http://127.0.0.1:8000/match_mydata', 
            json=test_data,
            headers={'Content-Type': 'application/json'}
        ) as response:
            end_time = time.time()
            
            print(f"HTML API call completed in {end_time - start_time:.2f} seconds")
            
            if response.status == 200:
                content = await response.text()
                print("✓ HTML endpoint works correctly")
                print(f"  Response length: {len(content)} characters")
                return True
            else:
                content = await response.text()
                print(f"⚠ HTML endpoint returned status {response.status}")
                print(f"  Response: {content[:200]}...")
                return False


async def test_concurrent_requests():
    """Test concurrent API requests to verify async performance."""
    async def make_request(session, request_id):
        test_data = {
            'response_type': 'json',
            'request_id': request_id,
            'test_param': f'concurrent_test_{request_id}'
        }
        
        start_time = time.time()
        async with session.post(
            'http://127.0.0.1:8000/match_mydata', 
            json=test_data,
            headers={'Content-Type': 'application/json'}
        ) as response:
            end_time = time.time()
            return {
                'request_id': request_id,
                'status': response.status,
                'duration': end_time - start_time,
                'success': response.status == 200
            }
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        
        # Make 3 concurrent requests
        tasks = [make_request(session, i) for i in range(3)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_results = [r for r in results if not isinstance(r, Exception) and r['success']]
        
        print(f"✓ {len(successful_results)}/3 concurrent requests completed successfully")
        print(f"  Total time: {total_time:.2f} seconds")
        print(f"  Average per request: {total_time/3:.2f} seconds")
        
        if successful_results:
            avg_duration = sum(r['duration'] for r in successful_results) / len(successful_results)
            print(f"  Average request duration: {avg_duration:.2f} seconds")
        
        return len(successful_results) >= 2  # Allow for some failures


async def main():
    """Run all API endpoint tests."""
    print("🚀 Starting API endpoint tests...\n")
    
    try:
        # Basic endpoint tests
        await test_root_endpoint()
        print()
        
        await test_prompt_save_endpoint()
        print()
        
        # Main functionality tests
        print("Testing main API endpoints...")
        json_success = await test_match_mydata_json()
        print()
        
        html_success = await test_match_mydata_html()
        print()
        
        # Concurrent request test
        print("Testing concurrent requests...")
        concurrent_success = await test_concurrent_requests()
        print()
        
        if json_success and html_success and concurrent_success:
            print("✅ All API endpoint tests passed!")
            return True
        else:
            print("⚠ Some tests had issues, but basic functionality works")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Make sure the FastAPI server is running on http://127.0.0.1:8000")
    print("Run: python -m uvicorn internalSkuScript:app --reload --port 8000\n")
    
    success = asyncio.run(main())
    exit(0 if success else 1)
