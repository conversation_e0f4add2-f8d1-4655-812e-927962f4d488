#!/usr/bin/env python3
"""
Test script to verify the parallel image processing limit works correctly.
"""

import asyncio
import time
import os
import sys
from unittest.mock import patch, AsyncMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.image_utils import encode_image_array_from_urls_to_base64, MAX_PARALLEL_IMAGE_REQUESTS


async def test_parallel_limit():
    """Test that the parallel processing limit is respected."""
    print(f"Testing parallel limit of {MAX_PARALLEL_IMAGE_REQUESTS} requests...")
    
    # Track concurrent requests
    concurrent_count = 0
    max_concurrent = 0
    
    async def mock_encode_image(session, url, target_dimensions):
        """Mock function that tracks concurrent execution."""
        nonlocal concurrent_count, max_concurrent
        
        concurrent_count += 1
        max_concurrent = max(max_concurrent, concurrent_count)
        
        print(f"  Processing {url} (concurrent: {concurrent_count})")
        
        # Simulate processing time
        await asyncio.sleep(0.1)
        
        concurrent_count -= 1
        return f"base64_data_for_{url}"
    
    # Create test URLs (more than the limit to test queueing)
    test_urls = [f"http://example.com/image{i}.jpg" for i in range(25)]
    
    # Patch the actual encoding function
    with patch('utils.image_utils.encode_image_from_url_to_base64', side_effect=mock_encode_image):
        start_time = time.time()
        results = await encode_image_array_from_urls_to_base64(test_urls)
        end_time = time.time()
    
    print(f"\nResults:")
    print(f"  Total URLs processed: {len(test_urls)}")
    print(f"  Successful results: {len([r for r in results if r is not None])}")
    print(f"  Max concurrent requests: {max_concurrent}")
    print(f"  Configured limit: {MAX_PARALLEL_IMAGE_REQUESTS}")
    print(f"  Total time: {end_time - start_time:.2f} seconds")
    
    # Verify the limit was respected
    if max_concurrent <= MAX_PARALLEL_IMAGE_REQUESTS:
        print(f"✅ PASS: Parallel limit respected ({max_concurrent} <= {MAX_PARALLEL_IMAGE_REQUESTS})")
        return True
    else:
        print(f"❌ FAIL: Parallel limit exceeded ({max_concurrent} > {MAX_PARALLEL_IMAGE_REQUESTS})")
        return False


async def test_different_limits():
    """Test with different limit values."""
    print("\nTesting with different limit values...")
    
    original_limit = MAX_PARALLEL_IMAGE_REQUESTS
    
    for test_limit in [3, 5, 10]:
        print(f"\nTesting with limit = {test_limit}")
        
        # Temporarily change the limit
        import utils.image_utils
        utils.image_utils.MAX_PARALLEL_IMAGE_REQUESTS = test_limit
        utils.image_utils._IMAGE_PROCESSING_SEMAPHORE = None  # Reset semaphore
        
        concurrent_count = 0
        max_concurrent = 0
        
        async def mock_encode_image(session, url, target_dimensions):
            nonlocal concurrent_count, max_concurrent
            concurrent_count += 1
            max_concurrent = max(max_concurrent, concurrent_count)
            await asyncio.sleep(0.05)
            concurrent_count -= 1
            return f"data_{url}"
        
        test_urls = [f"http://test.com/img{i}.jpg" for i in range(15)]
        
        with patch('utils.image_utils.encode_image_from_url_to_base64', side_effect=mock_encode_image):
            await encode_image_array_from_urls_to_base64(test_urls)
        
        print(f"  Max concurrent: {max_concurrent}, Limit: {test_limit}")
        
        if max_concurrent <= test_limit:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")
    
    # Restore original limit
    utils.image_utils.MAX_PARALLEL_IMAGE_REQUESTS = original_limit
    utils.image_utils._IMAGE_PROCESSING_SEMAPHORE = None


async def test_environment_variable():
    """Test that the limit can be set via environment variable."""
    print("\nTesting environment variable configuration...")
    
    # Test default value
    print(f"Current MAX_PARALLEL_IMAGE_REQUESTS: {MAX_PARALLEL_IMAGE_REQUESTS}")
    
    # Test with environment variable
    os.environ["MAX_PARALLEL_IMAGE_REQUESTS"] = "7"
    
    # Reload the module to pick up the new environment variable
    import importlib
    import utils.image_utils
    importlib.reload(utils.image_utils)
    
    new_limit = utils.image_utils.MAX_PARALLEL_IMAGE_REQUESTS
    print(f"After setting env var to 7: {new_limit}")
    
    if new_limit == 7:
        print("✅ PASS: Environment variable configuration works")
    else:
        print("❌ FAIL: Environment variable not respected")
    
    # Clean up
    del os.environ["MAX_PARALLEL_IMAGE_REQUESTS"]


async def main():
    """Run all tests."""
    print("🚀 Testing Parallel Image Processing Limits\n")
    print("=" * 50)
    
    try:
        # Test basic parallel limit
        test1_passed = await test_parallel_limit()
        
        # Test different limits
        await test_different_limits()
        
        # Test environment variable
        await test_environment_variable()
        
        print("\n" + "=" * 50)
        if test1_passed:
            print("✅ All parallel limit tests completed successfully!")
        else:
            print("⚠️  Some tests had issues, please review the output above.")
        
        print(f"\n💡 To change the limit, set the environment variable:")
        print(f"   export MAX_PARALLEL_IMAGE_REQUESTS=15")
        print(f"   or add it to your .env file")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
