import requests
import base64
import io
from PIL import Image
import concurrent.futures
import time
import warnings
import os
from dotenv import load_dotenv

load_dotenv()

# Default values are provided as a fallback, though they should be set in .env
IMAGE_WIDTH_HEIGHT = int(os.getenv("IMAGE_WIDTH_HEIGHT", 500))


# Suppress InsecureRequestWarning
warnings.filterwarnings("ignore", message="Unverified HTTPS request")

def encode_image_array_from_urls_to_base64(image_urls, target_dimensions=(IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)):
    """
    Encode multiple images from URLs in parallel.
    
    Args:
        image_urls (list): List of image URLs to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).
        
    Returns:
        list: List of base64 encoded images
    """
    start_time = time.time()  # Start timing the entire process
    
    # Split the URLs if they're in a single string separated by semicolons
    if isinstance(image_urls, str):
        image_urls = image_urls.split(';')
    
    # Filter out empty strings
    image_urls = [url for url in image_urls if url]
    
    import re

    def normalize_ebay_url(url):
        # try to pull ID from the “/z/<ID>/” form
        m = re.search(r'/z/([^/]+)/', url)
        if m:
            img_id = m.group(1)
        else:
            # otherwise, maybe it’s already in the “/images/g/<ID>/” form
            m2 = re.search(r'/images/g/([^/]+)/', url)
            if m2:
                img_id = m2.group(1)
            else:
                # if no ID found, leave it unchanged (or skip)
                return url
        return f"https://i.ebayimg.com/images/g/{img_id}/s-l{IMAGE_WIDTH_HEIGHT}.webp"

    # filter out any empty strings and normalize
    image_urls = [normalize_ebay_url(u) for u in image_urls if u]
    
    
    
    if not image_urls:
        return []
    
    # Use ThreadPoolExecutor to process images in parallel
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Submit all encoding tasks and store futures in order
        futures = [executor.submit(encode_image_from_url_to_base64, url, target_dimensions) for url in image_urls]
        
        # Collect results in the original order
        encoded_images = []
        for i, future in enumerate(futures):
            url = image_urls[i]  # Get the original URL for error reporting
            try:
                encoded_image = future.result()
                encoded_images.append(encoded_image)
            except Exception as e:
                print(f"Error processing image from {url}: {e}")
                encoded_images.append(None) # Or some other placeholder for failed images
    
    total_time = time.time() - start_time
    print(f"Total time to encode {len(image_urls)} images: {total_time:.2f} seconds")
    
    return encoded_images

def encode_image_from_url_to_base64(image_url, target_dimensions=(IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)):
    """
    Encode a single image from a URL.
    
    Args:
        image_url (str): URL of the image to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).
        
    Returns:
        str: Base64 encoded image
    """
    start_time = time.time()  # Start timing
    
    print(image_url)
    
    # Time the download
    download_start = time.time()
    response = requests.get(image_url, verify=False)
    download_time = time.time() - download_start
    
    if response.status_code == 200:
        # Time the image processing
        processing_start = time.time()
        
        # Load the image from the response content
        image = Image.open(io.BytesIO(response.content))
        
        # Resize the image to target_dimensions
        resized_image = resize_image(image, target_dimensions[0], target_dimensions[1])
        
        # Convert the resized image to bytes
        img_byte_arr = io.BytesIO()
        resized_image.save(img_byte_arr, format=image.format or 'JPEG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # Encode to base64
        base64_image = base64.b64encode(img_byte_arr).decode("utf-8")
        
        processing_time = time.time() - processing_start
        total_time = time.time() - start_time
        
        print(f"Image timing - Download: {download_time:.2f}s, Processing: {processing_time:.2f}s, Total: {total_time:.2f}s")
        
        return base64_image
    else:
        raise Exception(f"Failed to download image. Status code: {response.status_code}")

    
def resize_image(image, width, height):
    """
    Resize an image to the specified width and height only if it's larger than the target dimensions.
    Maintains aspect ratio when resizing.
    
    Args:
        image (PIL.Image): The image to resize
        width (int): Target width
        height (int): Target height
        
    Returns:
        PIL.Image: Resized image with dimensions not exceeding width x height while maintaining aspect ratio
    """
    # Get the original dimensions
    orig_width, orig_height = image.size
    
    # If the image is already smaller than the target dimensions in both width and height,
    # return the original image without resizing
    if orig_width <= width and orig_height <= height:
        return image
    
    # Calculate the scaling factor to maintain aspect ratio
    scale_width = width / orig_width
    scale_height = height / orig_height
    scale = min(scale_width, scale_height)
    
    # Calculate new dimensions
    new_width = int(orig_width * scale)
    new_height = int(orig_height * scale)
    
    # Resize the image while maintaining aspect ratio
    return image.resize((new_width, new_height), Image.LANCZOS)
