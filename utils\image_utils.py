import aiohttp
import asyncio
import base64
import io
from PIL import Image
import time
import warnings
import os
import re
from typing import List, Optional, Tuple
from dotenv import load_dotenv

load_dotenv()

# Default values are provided as a fallback, though they should be set in .env
IMAGE_WIDTH_HEIGHT = int(os.getenv("IMAGE_WIDTH_HEIGHT", 500))

# Suppress InsecureRequestWarning
warnings.filterwarnings("ignore", message="Unverified HTTPS request")


def normalize_ebay_url(url: str) -> str:
    """
    Normalize eBay image URLs to use the optimized format.
    
    Args:
        url (str): Original eBay image URL
        
    Returns:
        str: Normalized eBay image URL
    """
    # try to pull ID from the "/z/<ID>/" form
    m = re.search(r'/z/([^/]+)/', url)
    if m:
        img_id = m.group(1)
    else:
        # otherwise, maybe it's already in the "/images/g/<ID>/" form
        m2 = re.search(r'/images/g/([^/]+)/', url)
        if m2:
            img_id = m2.group(1)
        else:
            # if no ID found, leave it unchanged (or skip)
            return url
    return f"https://i.ebayimg.com/images/g/{img_id}/s-l{IMAGE_WIDTH_HEIGHT}.webp"


async def encode_image_array_from_urls_to_base64(
    image_urls: List[str], 
    target_dimensions: Tuple[int, int] = (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)
) -> List[Optional[str]]:
    """
    Encode multiple images from URLs in parallel using async/await.
    
    Args:
        image_urls (list): List of image URLs to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).
        
    Returns:
        list: List of base64 encoded images (None for failed images)
    """
    start_time = time.time()  # Start timing the entire process
    
    # Split the URLs if they're in a single string separated by semicolons
    if isinstance(image_urls, str):
        image_urls = image_urls.split(';')
    
    # Filter out empty strings and normalize
    image_urls = [normalize_ebay_url(u) for u in image_urls if u]
    
    if not image_urls:
        return []
    
    # Use asyncio.gather to process images in parallel
    async with aiohttp.ClientSession(
        connector=aiohttp.TCPConnector(ssl=False),
        timeout=aiohttp.ClientTimeout(total=30)
    ) as session:
        tasks = [
            encode_image_from_url_to_base64(session, url, target_dimensions) 
            for url in image_urls
        ]
        
        # Execute all tasks concurrently
        encoded_images = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        results = []
        for i, result in enumerate(encoded_images):
            url = image_urls[i]
            if isinstance(result, Exception):
                print(f"Error processing image from {url}: {result}")
                results.append(None)
            else:
                results.append(result)
    
    total_time = time.time() - start_time
    print(f"Total time to encode {len(image_urls)} images: {total_time:.2f} seconds")
    
    return results


async def encode_image_from_url_to_base64(
    session: aiohttp.ClientSession,
    image_url: str, 
    target_dimensions: Tuple[int, int] = (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)
) -> str:
    """
    Encode a single image from a URL using async HTTP request.
    
    Args:
        session (aiohttp.ClientSession): HTTP session for making requests
        image_url (str): URL of the image to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).
        
    Returns:
        str: Base64 encoded image
    """
    start_time = time.time()  # Start timing
    
    print(image_url)
    
    # Time the download
    download_start = time.time()
    async with session.get(image_url, ssl=False) as response:
        if response.status == 200:
            content = await response.read()
            download_time = time.time() - download_start
            
            # Time the image processing
            processing_start = time.time()
            
            # Load the image from the response content
            image = Image.open(io.BytesIO(content))
            
            # Resize the image to target_dimensions
            resized_image = resize_image(image, target_dimensions[0], target_dimensions[1])
            
            # Convert the resized image to bytes
            img_byte_arr = io.BytesIO()
            resized_image.save(img_byte_arr, format=image.format or 'JPEG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # Encode to base64
            base64_image = base64.b64encode(img_byte_arr).decode("utf-8")
            
            processing_time = time.time() - processing_start
            total_time = time.time() - start_time
            
            print(f"Image timing - Download: {download_time:.2f}s, Processing: {processing_time:.2f}s, Total: {total_time:.2f}s")
            
            return base64_image
        else:
            raise Exception(f"Failed to download image. Status code: {response.status}")


def resize_image(image, width, height):
    """
    Resize an image to the specified width and height only if it's larger than the target dimensions.
    Maintains aspect ratio when resizing.
    
    Args:
        image (PIL.Image): The image to resize
        width (int): Target width
        height (int): Target height
        
    Returns:
        PIL.Image: Resized image with dimensions not exceeding width x height while maintaining aspect ratio
    """
    # Get the original dimensions
    orig_width, orig_height = image.size
    
    # If the image is already smaller than the target dimensions in both width and height,
    # return the original image without resizing
    if orig_width <= width and orig_height <= height:
        return image
    
    # Calculate the scaling factor to maintain aspect ratio
    scale_width = width / orig_width
    scale_height = height / orig_height
    scale = min(scale_width, scale_height)
    
    # Calculate new dimensions
    new_width = int(orig_width * scale)
    new_height = int(orig_height * scale)
    
    # Resize the image while maintaining aspect ratio
    return image.resize((new_width, new_height), Image.LANCZOS)
