#!/usr/bin/env python3
"""
Final verification script to ensure the entire application runs asynchronously.
"""

import asyncio
import ast
import os
import sys
from pathlib import Path


def analyze_file_for_blocking_operations(file_path):
    """Analyze a Python file for potentially blocking operations."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        blocking_operations = []
        async_functions = []
        
        class BlockingOperationVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                if node.name.startswith('_') or node.name in ['main', '__init__']:
                    pass  # Skip private and special methods
                else:
                    # Check if function is async
                    if isinstance(node, ast.AsyncFunctionDef):
                        async_functions.append(node.name)
                    else:
                        # Check if it contains I/O operations
                        for child in ast.walk(node):
                            if isinstance(child, ast.Call):
                                if isinstance(child.func, ast.Name):
                                    if child.func.id in ['open', 'requests.get', 'requests.post']:
                                        blocking_operations.append({
                                            'function': node.name,
                                            'operation': child.func.id,
                                            'line': child.lineno
                                        })
                                elif isinstance(child.func, ast.Attribute):
                                    if child.func.attr in ['get', 'post', 'put', 'delete'] and \
                                       isinstance(child.func.value, ast.Name) and \
                                       child.func.value.id == 'requests':
                                        blocking_operations.append({
                                            'function': node.name,
                                            'operation': f'requests.{child.func.attr}',
                                            'line': child.lineno
                                        })
                self.generic_visit(node)
        
        visitor = BlockingOperationVisitor()
        visitor.visit(tree)
        
        return {
            'file': file_path,
            'blocking_operations': blocking_operations,
            'async_functions': async_functions,
            'has_async': len(async_functions) > 0
        }
    
    except Exception as e:
        return {
            'file': file_path,
            'error': str(e),
            'blocking_operations': [],
            'async_functions': [],
            'has_async': False
        }


def check_imports_for_async_libraries(file_path):
    """Check if file imports async libraries."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        async_imports = []
        if 'import aiohttp' in content or 'from aiohttp' in content:
            async_imports.append('aiohttp')
        if 'import aiofiles' in content or 'from aiofiles' in content:
            async_imports.append('aiofiles')
        if 'import asyncio' in content or 'from asyncio' in content:
            async_imports.append('asyncio')
        if 'async def' in content:
            async_imports.append('async_functions')
        if 'await ' in content:
            async_imports.append('await_usage')
        
        return async_imports
    
    except Exception as e:
        return []


def verify_async_implementation():
    """Verify the async implementation across all relevant files."""
    
    # Files to check
    files_to_check = [
        'llm_analyzer/llm_analyzer.py',
        'llm_analyzer/cache_manager.py',
        'llm_analyzer/analysis_renderer.py',
        'utils/image_utils.py',
        'utils/prompt_editor.py',
        'internalSkuScript.py'
    ]
    
    print("🔍 Verifying async implementation across codebase...\n")
    
    results = {}
    total_async_functions = 0
    total_blocking_operations = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"📁 Analyzing {file_path}...")
            
            # Check for blocking operations
            analysis = analyze_file_for_blocking_operations(file_path)
            
            # Check for async imports
            async_imports = check_imports_for_async_libraries(file_path)
            
            results[file_path] = {
                'analysis': analysis,
                'async_imports': async_imports
            }
            
            # Report findings
            if analysis.get('error'):
                print(f"  ❌ Error analyzing file: {analysis['error']}")
            else:
                async_funcs = len(analysis['async_functions'])
                blocking_ops = len(analysis['blocking_operations'])
                
                total_async_functions += async_funcs
                total_blocking_operations += blocking_ops
                
                print(f"  ✅ Async functions: {async_funcs}")
                print(f"  📊 Async imports: {', '.join(async_imports) if async_imports else 'None'}")
                
                if blocking_ops > 0:
                    print(f"  ⚠️  Potential blocking operations: {blocking_ops}")
                    for op in analysis['blocking_operations']:
                        print(f"     - {op['operation']} in {op['function']}() at line {op['line']}")
                else:
                    print(f"  ✅ No blocking operations detected")
            
            print()
        else:
            print(f"❌ File not found: {file_path}\n")
    
    return results, total_async_functions, total_blocking_operations


def check_requirements():
    """Check if async dependencies are in requirements.txt."""
    print("📋 Checking requirements.txt for async dependencies...")
    
    required_async_deps = ['aiohttp', 'aiofiles', 'aioredis']
    
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
        
        missing_deps = []
        for dep in required_async_deps:
            if dep not in requirements:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"  ⚠️  Missing dependencies: {', '.join(missing_deps)}")
            return False
        else:
            print(f"  ✅ All async dependencies present: {', '.join(required_async_deps)}")
            return True
    
    except FileNotFoundError:
        print("  ❌ requirements.txt not found")
        return False


def verify_test_files():
    """Verify test files exist and are functional."""
    print("🧪 Checking test files...")
    
    test_files = [
        'test_async_implementation.py',
        'test_api_endpoints.py'
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"  ✅ {test_file} exists")
        else:
            print(f"  ❌ {test_file} missing")


async def run_quick_async_test():
    """Run a quick test to verify async functionality."""
    print("⚡ Running quick async functionality test...")
    
    try:
        # Test async sleep (basic async operation)
        start_time = asyncio.get_event_loop().time()
        await asyncio.sleep(0.1)
        end_time = asyncio.get_event_loop().time()
        
        if end_time - start_time >= 0.1:
            print("  ✅ Basic async operations working")
        
        # Test concurrent operations
        async def quick_task(n):
            await asyncio.sleep(0.05)
            return n * 2
        
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(*[quick_task(i) for i in range(3)])
        end_time = asyncio.get_event_loop().time()
        
        if end_time - start_time < 0.2 and results == [0, 2, 4]:
            print("  ✅ Concurrent async operations working")
            return True
        else:
            print("  ⚠️  Concurrent operations may have issues")
            return False
    
    except Exception as e:
        print(f"  ❌ Async test failed: {e}")
        return False


async def main():
    """Run complete verification."""
    print("🚀 Final Async Implementation Verification\n")
    print("=" * 50)
    
    # Check requirements
    req_ok = check_requirements()
    print()
    
    # Verify async implementation
    results, total_async_funcs, total_blocking_ops = verify_async_implementation()
    
    # Check test files
    verify_test_files()
    print()
    
    # Run quick async test
    async_test_ok = await run_quick_async_test()
    print()
    
    # Summary
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    print(f"✅ Total async functions found: {total_async_funcs}")
    print(f"⚠️  Total potential blocking operations: {total_blocking_ops}")
    print(f"📋 Requirements check: {'✅ PASS' if req_ok else '❌ FAIL'}")
    print(f"⚡ Async functionality test: {'✅ PASS' if async_test_ok else '❌ FAIL'}")
    
    # Overall assessment
    if total_async_funcs > 0 and total_blocking_ops == 0 and req_ok and async_test_ok:
        print("\n🎉 OVERALL RESULT: ✅ ASYNC IMPLEMENTATION VERIFIED")
        print("   The application has been successfully refactored to be non-blocking!")
        return True
    elif total_async_funcs > 0 and total_blocking_ops <= 2:
        print("\n✅ OVERALL RESULT: ✅ ASYNC IMPLEMENTATION MOSTLY COMPLETE")
        print("   Minor blocking operations may exist but core functionality is async.")
        return True
    else:
        print("\n❌ OVERALL RESULT: ⚠️  ASYNC IMPLEMENTATION NEEDS ATTENTION")
        print("   Some blocking operations or missing dependencies detected.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
