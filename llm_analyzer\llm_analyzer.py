import json5
import os
# import time # No longer needed here, moved to CacheManager

from litellm import acompletion, completion_cost
from litellm import exceptions as litellm_exceptions

from typing import Dict, Any, List, Optional
from datetime import datetime
from urllib.parse import unquote_plus # Import for URL decoding
from utils.image_utils import encode_image_array_from_urls_to_base64
from .cache_manager import CacheManager # Import the new CacheManager


class LLMAnalyzer:
    """Analyzes listings using AI models, configurable for different product types."""

    def __init__(self) -> None:
        """
        Initialize a model analyzer using LiteLLM.
        """
        # Store the base directory for finding system message files
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Create system message path in _prompts directory if it doesn't exist
        self._ensure_system_message()

        # Read system message from file
        system_message_path = self.get_system_message_path()

        # Print the path for debugging purposes
        print(f"Looking for system message at: {system_message_path}")

        try:
            with open(system_message_path, "r") as f:
                self.__SYSTEM_MESSAGE = f.read()
        except FileNotFoundError:
            print(f"Warning: System message file not found at {system_message_path}. Using default message.")
            self.__SYSTEM_MESSAGE = "Analyze the provided information and return a JSON response."

        # Configure LiteLLM
        self.model_string = os.getenv("LITELLM_MODEL_STRING")
        if not self.model_string:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] WARNING: LITELLM_MODEL_STRING environment variable not set. LiteLLM calls may fail.")
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM configured to use model: {self.model_string}")

        # Initialize CacheManager
        self.cache_manager = CacheManager()

    def _ensure_system_message(self):
        """Ensure the system message file exists for this product type."""
        prompts_dir = os.path.join(self.base_dir, "_prompts")
        os.makedirs(prompts_dir, exist_ok=True)

        # Path to the product-specific system message
        system_message_path = os.path.join(prompts_dir, "system_message.txt")
        default_message_path = os.path.join(prompts_dir, "default_system_message.txt")

        # If system message doesn't exist, copy from default or create a generic one
        if not os.path.exists(system_message_path):
            if os.path.exists(default_message_path):
                # Copy default to system message
                with open(default_message_path, 'r') as src, open(system_message_path, 'w') as dst:
                    dst.write(src.read())
            else:
                # Create a generic system message
                generic_message = f"""You are an AI designed to analyze product listings.

Examine the provided information and images to determine if there are any issues.

Return a JSON object with your assessment.
"""
                with open(system_message_path, 'w') as f:
                    f.write(generic_message)

                # Also create the default message file
                with open(default_message_path, 'w') as f:
                    f.write(generic_message)

    def get_system_message_path(self) -> str:
        """Return the path to the product-specific system message file."""
        # Path to the product-specific system message file within the _prompts directory
        prompts_dir = os.path.join(self.base_dir, "_prompts")
        return os.path.join(prompts_dir, "system_message.txt")

    def get_default_response(self, error: Optional[str] = None) -> Dict[str, Any]:
        """Return a generic default response structure."""
        response = {
            "Status": "OK",
            "Visual evaluation": False
        }
        if error:
            response["error"] = error
        return response

    def get_system_message(self) -> str:
        """Return the current system message"""
        return self.__SYSTEM_MESSAGE

    def update_system_message(self, new_message: str) -> None:
        """Update the system message and save it to file"""
        # Normalize line endings
        normalized_message = new_message.replace('\r\n', '\n')
        self.__SYSTEM_MESSAGE = normalized_message

        # Save to file
        # Save to file. Subclasses must implement get_system_message_path
        # to return the full path to the system message file.
        system_message_path = self.get_system_message_path()

        with open(system_message_path, "w") as f:
            f.write(normalized_message)

    async def __call__(self, params: Dict[str, Any]) -> Dict[str, Any]:
        # Decode URL-encoded string values in params, but leave image URLs as they are.
        decoded_params: Dict[str, Any] = {}
        for key, value in params.items():
            if isinstance(value, str) and key != "images": # Assuming 'images' key holds a list of URLs or a single URL string that shouldn't be decoded here
                decoded_params[key] = unquote_plus(value)
            elif isinstance(value, list) and key == "images": # If 'images' is a list of URLs
                # We assume URLs in the list should not be unquoted themselves,
                # but if individual string items *within* params (not image URLs) were lists, they'd need deeper handling.
                # For now, copy the list of URLs as is.
                decoded_params[key] = value
            else:
                decoded_params[key] = value
        
        content: str = ""  # Initialize content to an empty string with type hint

        if not self.model_string:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: LITELLM_MODEL_STRING not configured in environment variables.")
            return self.get_default_response(error="LITELLM_MODEL_STRING not configured.")

        # Generate cache key using the CacheManager.generate_key method.
        # Pass the decoded_params for cache key generation.
        cache_key = self.cache_manager.generate_key(
            model_string=self.model_string,
            system_message=self.__SYSTEM_MESSAGE,
            request_params=decoded_params
        )

        # Check cache using CacheManager
        cached_response = self.cache_manager.get(cache_key)
        if cached_response is not None:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Returning cached response for key: {cache_key[:50]}...")
            return cached_response

        print(f"[{datetime.now().strftime('%H:%M:%S')}] Cache miss for key: {cache_key[:50]}... Making fresh API call.")
        
        # If cache miss, proceed to prepare for API call.
        # First, extract image URLs (from decoded_params, though they weren't decoded) and encode them if present.
        image_urls: Optional[List[str]] = decoded_params.get("images")
        base64_images: List[str] = []
        if image_urls:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Encoding images for API call...")
            base64_images = encode_image_array_from_urls_to_base64(image_urls)

        # Prepare user text prompt using decoded_params (excluding images)
        text_only_params = {k: v for k, v in decoded_params.items() if k != "images"}
        user_text_for_api = ", ".join([f"{k}: {v}" for k, v in text_only_params.items() if v])

        # Construct actual messages for LiteLLM API call
        messages_for_api = [{"role": "system", "content": self.__SYSTEM_MESSAGE}]
        user_content_parts_for_api = [{"type": "text", "text": user_text_for_api}]
        if base64_images:
            for base64_img in base64_images:
                user_content_parts_for_api.append({
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{base64_img}"}
                })
        messages_for_api.append({"role": "user", "content": user_content_parts_for_api})

        llm_output_content = ""
        llm_cost_usd: Optional[float] = None
        try:
            start_time = datetime.now()
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Calling LiteLLM with model: {self.model_string}")
            # print(f"[{datetime.now().strftime('%H:%M:%S')}] Messages for API: {json5.dumps(messages_for_api, indent=2)}") # For debugging

            response_litellm = await acompletion(
                model=self.model_string,
                response_format={ "type": "json_object" },
                messages=messages_for_api)
            
            if response_litellm:
                try:
                    cost = completion_cost(completion_response=response_litellm)
                    if cost is not None:
                        llm_cost_usd = float(cost)
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM API call cost: ${llm_cost_usd:.10f}")
                except Exception as e:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] Warning: Could not calculate LiteLLM cost: {e}")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM API call completed in {duration:.2f} seconds")

            if response_litellm.choices and response_litellm.choices[0].message and response_litellm.choices[0].message.content:
                llm_output_content = response_litellm.choices[0].message.content
                print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM response: {llm_output_content}")
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM response does not contain expected content structure.")
                return self.get_default_response(error="LiteLLM response structure invalid.")
            
            content = llm_output_content

        except litellm_exceptions.APIConnectionError as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM API Connection Error: {e}")
            return self.get_default_response(error=f"API Connection Error: {e}")
        except litellm_exceptions.RateLimitError as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM Rate Limit Error: {e}")
            return self.get_default_response(error=f"Rate Limit Error: {e}")
        except litellm_exceptions.APIError as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] LiteLLM API Error: {e}")
            return self.get_default_response(error=f"LiteLLM API Error: {e}")
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] An unexpected error occurred with LiteLLM: {e}")
            return self.get_default_response(error=f"Unexpected error with LiteLLM: {e}")

        if content.startswith("```") and content.endswith("```"):
            content = content.strip('`')
            if content.startswith("json\n"):
                content = content[5:]
            content = content.strip()

        try:
            result = json5.loads(content)
            if not isinstance(result, dict):
                print(f"[{datetime.now():%H:%M:%S}] Unexpected response format: {result}")
                return self.get_default_response(error="Response format invalid")
            
            if llm_cost_usd is not None:
                result["llm_cost_usd"] = llm_cost_usd
            
            # Store successful response in cache using CacheManager
            # The CacheManager's default expiration will be used (120 minutes)
            self.cache_manager.set(cache_key, result)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Stored response in cache for key: {cache_key[:50]}...")
            return result

        except ValueError as e:
            print(f"[{datetime.now():%H:%M:%S}] JSON parsing error: {e}")
            print(f"[{datetime.now():%H:%M:%S}] Content received: {content}")
            return self.get_default_response(error="Failed to parse response")
