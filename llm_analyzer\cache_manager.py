import os
import json5
import time
import hashlib # Import the hashlib module
import async<PERSON>
from typing import Dict, Any, List, Optional
from disk<PERSON>che import <PERSON>ache
from concurrent.futures import ThreadPoolExecutor

# Define the directory for the cache, relative to this file's location
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".llm_cache")

class CacheManager:
    """Manages persistent on-disk caching for LLM responses with async interface."""

    def __init__(self, cache_dir: str = CACHE_DIR, default_expire_seconds: int = 120 * 60):
        """
        Initialize the CacheManager.

        Args:
            cache_dir (str): Directory to store cache files.
            default_expire_seconds (int): Default expiration time for cache entries in seconds.
        """
        os.makedirs(cache_dir, exist_ok=True)
        self.cache = Cache(cache_dir)
        self.default_expire_seconds = default_expire_seconds
        self._executor = ThreadPoolExecutor(max_workers=2)
        print(f"Disk cache initialized at: {cache_dir}")

    def generate_key(self,
                       model_string: Optional[str],
                       system_message: str,
                       request_params: Dict[str, Any]
                       ) -> str:
        """
        Generates a unique cache key based on the model, system message, and request parameters.

        Args:
            model_string (Optional[str]): The model string being used.
            system_message (str): The system message provided to the LLM.
            request_params (Dict[str, Any]): The parameters forming the user's request,
                                             which may include text and image URLs (e.g., under an 'images' key).
        Returns:
            str: A unique cache key.
        """
        if not model_string:
            model_string = "unknown_model"  # Fallback if model_string is None

        # Process request_params for consistent key generation
        # Make a copy to avoid modifying the original dictionary
        processed_params = dict(request_params)
        processed_params.pop("response_type", None)

        # If 'images' key exists and is a list, sort it for consistency
        if "images" in processed_params and isinstance(processed_params["images"], list):
            processed_params["images"] = sorted(processed_params["images"])

        # Create a dictionary of the core components for the cache key
        key_components = {
            "model": model_string,
            "system_message": system_message,
            "request_params": processed_params # Use the processed params
        }

        try:
            # Serialize the components dictionary to a string for the key.
            # sort_keys=True ensures that the order of keys in the dictionary (and nested dicts)
            # doesn't affect the output string, leading to consistent cache keys.
            components_str = json5.dumps(key_components, sort_keys=True)
        except Exception as e:
            # Fallback for non-serializable content
            print(f"Warning: Could not serialize components for cache key: {e}")
            # A more robust fallback might involve hashing individual problematic components
            # or using a simpler string representation. For now, str() is a basic fallback.
            components_str = str(key_components)

        # Generate a SHA256 hash of the serialized components string
        # Encode the string to bytes (UTF-8 is a common choice) before hashing
        hashed_components = hashlib.sha256(components_str.encode('utf-8')).hexdigest()

        return f"llm_request_hash::{hashed_components}"

    async def get(self, key: str) -> Optional[Any]:
        """
        Retrieves an item from the cache asynchronously.

        Args:
            key (str): The cache key.

        Returns:
            Optional[Any]: The cached item, or None if not found or expired.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, self.cache.get, key, None)

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """
        Stores an item in the cache asynchronously.

        Args:
            key (str): The cache key.
            value (Any): The value to store.
            expire (Optional[int]): Expiration time in seconds. Uses default if None.
        """
        if expire is None:
            expire = self.default_expire_seconds
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self._executor, self._set_sync, key, value, expire)

    def _set_sync(self, key: str, value: Any, expire: int) -> None:
        """Synchronous helper for cache set operation."""
        self.cache.set(key, value, expire=expire)

    async def delete(self, key: str) -> None:
        """
        Deletes an item from the cache asynchronously.

        Args:
            key (str): The cache key.
        """
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self._executor, self.cache.delete, key)

    async def clear(self) -> None:
        """Clears all items from the cache asynchronously."""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self._executor, self.cache.clear)

    def close(self) -> None:
        """Closes the cache connection and executor."""
        self.cache.close()
        self._executor.shutdown(wait=True)

if __name__ == '__main__':
    # Example Usage
    manager = CacheManager(default_expire_seconds=5) # Short expiry for testing

    test_system_message = "You are a helpful assistant."
    test_model = "gpt-3.5-turbo"
    test_request_params_1 = {
        "prompt": "Hello there!",
        "images": ["http://example.com/image2.jpg", "http://example.com/image1.jpg"], # Unsorted
        "temperature": 0.7
    }

    key1 = manager.generate_key(
        model_string=test_model,
        system_message=test_system_message,
        request_params=test_request_params_1
    )
    print(f"Generated key: {key1}")

    # Test set and get
    if manager.get(key1) is None:
        print("Cache miss for key1. Setting value.")
        manager.set(key1, {"response": "Hi!"})
    else:
        print("Cache hit for key1.")

    print(f"Value for key1: {manager.get(key1)}")

    time.sleep(3)
    print(f"Value for key1 after 3s: {manager.get(key1)}")

    time.sleep(3) # Total > 5s
    print(f"Value for key1 after 6s (should be None if expired): {manager.get(key1)}")

    # Test another key
    test_request_params_2 = {
        "prompt": "Another prompt",
        # No "images" key this time
        "max_tokens": 100
    }
    key2 = manager.generate_key(
        model_string=test_model,
        system_message=test_system_message,
        request_params=test_request_params_2
    )
    manager.set(key2, {"response": "Another answer"}, expire=10)
    print(f"Value for key2: {manager.get(key2)}")

    manager.close()
    print("Cache manager closed.")